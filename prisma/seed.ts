import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Clear existing data
  await prisma.notification.deleteMany()
  await prisma.userActivity.deleteMany()
  await prisma.matchCompatibility.deleteMany()
  await prisma.message.deleteMany()
  await prisma.match.deleteMany()
  await prisma.report.deleteMany()
  await prisma.blockedUser.deleteMany()
  await prisma.payment.deleteMany()
  await prisma.profilePicture.deleteMany()
  await prisma.userVerification.deleteMany()
  await prisma.discoveryPreferences.deleteMany()
  await prisma.searchHistory.deleteMany()
  await prisma.preferences.deleteMany()
  await prisma.children.deleteMany()
  await prisma.profile.deleteMany()
  await prisma.user.deleteMany()
  await prisma.town.deleteMany()
  await prisma.subscriptionPlan.deleteMany()

  console.log('🗑️ Cleared existing data')

  // Create Kenyan Towns
  const towns = await Promise.all([
    prisma.town.create({
      data: {
        name: 'Nairobi',
        county: 'Nairobi',
        latitude: -1.2921,
        longitude: 36.8219
      }
    }),
    prisma.town.create({
      data: {
        name: 'Mombasa',
        county: 'Mombasa',
        latitude: -4.0435,
        longitude: 39.6682
      }
    }),
    prisma.town.create({
      data: {
        name: 'Kisumu',
        county: 'Kisumu',
        latitude: -0.0917,
        longitude: 34.7680
      }
    }),
    prisma.town.create({
      data: {
        name: 'Nakuru',
        county: 'Nakuru',
        latitude: -0.3031,
        longitude: 36.0800
      }
    }),
    prisma.town.create({
      data: {
        name: 'Eldoret',
        county: 'Uasin Gishu',
        latitude: 0.5204,
        longitude: 35.2699
      }
    }),
    prisma.town.create({
      data: {
        name: 'Thika',
        county: 'Kiambu',
        latitude: -1.0333,
        longitude: 37.0833
      }
    }),
    prisma.town.create({
      data: {
        name: 'Kakamega',
        county: 'Kakamega',
        latitude: 0.2833,
        longitude: 34.7500
      }
    }),
    prisma.town.create({
      data: {
        name: 'Nyeri',
        county: 'Nyeri',
        latitude: -0.4167,
        longitude: 36.9500
      }
    })
  ])

  console.log('🏘️ Created towns')

  // Create Subscription Plans
  const plans = await Promise.all([
    prisma.subscriptionPlan.create({
      data: {
        name: 'Basic',
        price: 500,
        duration: 30,
        features: ['Unlimited profile views', 'Basic matching', '10 messages per day']
      }
    }),
    prisma.subscriptionPlan.create({
      data: {
        name: 'Premium',
        price: 1500,
        duration: 120,
        features: ['All Basic features', 'Unlimited messages', 'See who liked you', 'Advanced filters']
      }
    }),
    prisma.subscriptionPlan.create({
      data: {
        name: 'VIP',
        price: 3000,
        duration: 120,
        features: ['All Premium features', 'VIP badge', 'Priority support', 'Personal matchmaker']
      }
    })
  ])

  console.log('💳 Created subscription plans')

  // Create Admin User
  const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
  const adminPasswordRaw = process.env.ADMIN_PASSWORD || 'changeme'; // Fallback for local/dev only
  const adminPassword = await bcrypt.hash(adminPasswordRaw, 12);
  const admin = await prisma.user.create({
    data: {
      email: adminEmail,
      phone: '254700000001',
      passwordHash: adminPassword,
      name: 'Admin User',
      role: 'ADMIN',
      isAdmin: true,
      isPaid: true,
      subscriptionExpiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
      profile: {
        create: {
          fullName: 'Admin User',
          gender: 'MALE',
          genderPreference: 'FEMALE',
          dateOfBirth: new Date('1990-01-01'),
          townId: towns[0].id, // Nairobi
          bio: 'Platform administrator for KenyaMatch',
          tribe: 'Kikuyu',
          religion: 'Christian',
          occupation: 'Administrator',
          education: 'University',
          relationshipGoal: 'serious',
          verified: true,
          safetyScore: 100,
          isOnline: true
        }
      },
      discoveryPreferences: {
        create: {
          showMeTo: 'everyone',
          allowMessagesFrom: 'matches',
          profileVisibility: 'public',
          locationSharing: true,
          onlineStatus: true,
          lastSeen: true
        }
      }
    }
  })

  console.log('👨‍💼 Created admin user')

  // Create Sample Users
  const DEMO_USER_PASSWORD = process.env.DEMO_USER_PASSWORD || 'demo12345'; // For demo/dev only
  const sampleUsers = [
    {
      email: '<EMAIL>',
      phone: '254700000002',
      name: 'Sarah Kamau',
      gender: 'FEMALE',
      genderPreference: 'MALE',
      dateOfBirth: '1995-03-15',
      townIndex: 0, // Nairobi
      bio: 'Love traveling, cooking, and meeting new people. Looking for someone who shares similar values and goals.',
      tribe: 'Kikuyu',
      religion: 'Christian',
      occupation: 'Marketing Manager',
      education: 'University',
      relationshipGoal: 'marriage',
      hasChildren: false,
      isPaid: true
    },
    {
      email: '<EMAIL>',
      phone: '254700000003',
      name: 'James Odhiambo',
      gender: 'MALE',
      genderPreference: 'FEMALE',
      dateOfBirth: '1992-07-22',
      townIndex: 1, // Mombasa
      bio: 'Software engineer who loves technology and outdoor activities. Seeking a genuine connection.',
      tribe: 'Luo',
      religion: 'Christian',
      occupation: 'Software Engineer',
      education: 'University',
      relationshipGoal: 'serious',
      hasChildren: false,
      isPaid: false
    },
    {
      email: '<EMAIL>',
      phone: '254700000004',
      name: 'Faith Wanjiku',
      gender: 'FEMALE',
      genderPreference: 'MALE',
      dateOfBirth: '1998-11-08',
      townIndex: 2, // Kisumu
      bio: 'Nurse by profession, passionate about helping others. Looking for someone kind and caring.',
      tribe: 'Kikuyu',
      religion: 'Christian',
      occupation: 'Nurse',
      education: 'College',
      relationshipGoal: 'serious',
      hasChildren: true,
      isPaid: true
    },
    {
      email: '<EMAIL>',
      phone: '254700000005',
      name: 'David Mutua',
      gender: 'MALE',
      genderPreference: 'FEMALE',
      dateOfBirth: '1990-12-03',
      townIndex: 3, // Nakuru
      bio: 'Business owner and entrepreneur. Love sports and good conversation.',
      tribe: 'Kamba',
      religion: 'Christian',
      occupation: 'Business Owner',
      education: 'University',
      relationshipGoal: 'marriage',
      hasChildren: false,
      isPaid: true
    },
    {
      email: '<EMAIL>',
      phone: '254700000006',
      name: 'Grace Akinyi',
      gender: 'FEMALE',
      genderPreference: 'MALE',
      dateOfBirth: '1993-05-18',
      townIndex: 4, // Eldoret
      bio: 'Teacher who loves reading and nature walks. Seeking someone with similar interests.',
      tribe: 'Luo',
      religion: 'Christian',
      occupation: 'Teacher',
      education: 'University',
      relationshipGoal: 'serious',
      hasChildren: false,
      isPaid: false
    },
    {
      email: '<EMAIL>',
      phone: '254700000007',
      name: 'Peter Kiprop',
      gender: 'MALE',
      genderPreference: 'FEMALE',
      dateOfBirth: '1988-09-12',
      townIndex: 5, // Thika
      bio: 'Architect with a passion for design and travel. Looking for someone to share life adventures with.',
      tribe: 'Kalenjin',
      religion: 'Christian',
      occupation: 'Architect',
      education: 'University',
      relationshipGoal: 'marriage',
      hasChildren: true,
      isPaid: true
    },
    {
      email: '<EMAIL>',
      phone: '254700000008',
      name: 'Mercy Wambui',
      gender: 'FEMALE',
      genderPreference: 'MALE',
      dateOfBirth: '1996-02-25',
      townIndex: 6, // Kakamega
      bio: 'Lawyer who enjoys hiking and cooking. Seeking someone honest and ambitious.',
      tribe: 'Luhya',
      religion: 'Christian',
      occupation: 'Lawyer',
      education: 'University',
      relationshipGoal: 'serious',
      hasChildren: false,
      isPaid: true
    },
    {
      email: '<EMAIL>',
      phone: '254700000009',
      name: 'John Mwangi',
      gender: 'MALE',
      genderPreference: 'FEMALE',
      dateOfBirth: '1991-06-30',
      townIndex: 7, // Nyeri
      bio: 'Doctor who loves music and sports. Looking for someone kind and family-oriented.',
      tribe: 'Kikuyu',
      religion: 'Christian',
      occupation: 'Doctor',
      education: 'University',
      relationshipGoal: 'marriage',
      hasChildren: false,
      isPaid: false
    },
    {
      email: '<EMAIL>',
      phone: '254700000010',
      name: 'Linda Chebet',
      gender: 'FEMALE',
      genderPreference: 'MALE',
      dateOfBirth: '1994-08-14',
      townIndex: 0, // Nairobi
      bio: 'Financial analyst who enjoys yoga and coffee. Seeking someone ambitious and caring.',
      tribe: 'Kalenjin',
      religion: 'Christian',
      occupation: 'Financial Analyst',
      education: 'University',
      relationshipGoal: 'serious',
      hasChildren: false,
      isPaid: true
    },
    {
      email: '<EMAIL>',
      phone: '254700000011',
      name: 'Kevin Otieno',
      gender: 'MALE',
      genderPreference: 'FEMALE',
      dateOfBirth: '1989-03-22',
      townIndex: 1, // Mombasa
      bio: 'Chef who loves cooking and beach activities. Looking for someone who appreciates good food and adventure.',
      tribe: 'Luo',
      religion: 'Christian',
      occupation: 'Chef',
      education: 'College',
      relationshipGoal: 'serious',
      hasChildren: false,
      isPaid: false
    },
    {
      email: '<EMAIL>',
      phone: '254700000012',
      name: 'Ann Wairimu',
      gender: 'FEMALE',
      genderPreference: 'MALE',
      dateOfBirth: '1997-12-05',
      townIndex: 2, // Kisumu
      bio: 'Journalist passionate about storytelling and social justice. Seeking someone with similar values.',
      tribe: 'Kikuyu',
      religion: 'Christian',
      occupation: 'Journalist',
      education: 'University',
      relationshipGoal: 'serious',
      hasChildren: false,
      isPaid: true
    },
    {
      email: '<EMAIL>',
      phone: '254700000013',
      name: 'Brian Kimani',
      gender: 'MALE',
      genderPreference: 'FEMALE',
      dateOfBirth: '1993-10-18',
      townIndex: 3, // Nakuru
      bio: 'IT consultant who loves gaming and hiking. Looking for someone who shares my interests.',
      tribe: 'Kikuyu',
      religion: 'Christian',
      occupation: 'IT Consultant',
      education: 'University',
      relationshipGoal: 'marriage',
      hasChildren: false,
      isPaid: true
    },
    {
      email: '<EMAIL>',
      phone: '254700000014',
      name: 'Rose Akoth',
      gender: 'FEMALE',
      genderPreference: 'MALE',
      dateOfBirth: '1996-04-30',
      townIndex: 4, // Eldoret
      bio: 'Pharmacist who enjoys reading and traveling. Seeking someone kind and family-oriented.',
      tribe: 'Luo',
      religion: 'Christian',
      occupation: 'Pharmacist',
      education: 'University',
      relationshipGoal: 'marriage',
      hasChildren: false,
      isPaid: false
    },
    {
      email: '<EMAIL>',
      phone: '254700000015',
      name: 'Daniel Kipchirchir',
      gender: 'MALE',
      genderPreference: 'FEMALE',
      dateOfBirth: '1990-07-12',
      townIndex: 5, // Thika
      bio: 'Engineer who loves running and technology. Looking for someone to share life with.',
      tribe: 'Kalenjin',
      religion: 'Christian',
      occupation: 'Engineer',
      education: 'University',
      relationshipGoal: 'serious',
      hasChildren: true,
      isPaid: true
    },
    {
      email: '<EMAIL>',
      phone: '************',
      name: 'Esther Musyoki',
      gender: 'FEMALE',
      genderPreference: 'MALE',
      dateOfBirth: '1995-01-25',
      townIndex: 6, // Kakamega
      bio: 'Accountant who enjoys dancing and cooking. Seeking someone honest and loving.',
      tribe: 'Kamba',
      religion: 'Christian',
      occupation: 'Accountant',
      education: 'University',
      relationshipGoal: 'serious',
      hasChildren: false,
      isPaid: true
    },
    {
      email: '<EMAIL>',
      phone: '************',
      name: 'Michael Wafula',
      gender: 'MALE',
      genderPreference: 'FEMALE',
      dateOfBirth: '1988-11-08',
      townIndex: 7, // Nyeri
      bio: 'Teacher who loves football and community service. Looking for someone with similar values.',
      tribe: 'Luhya',
      religion: 'Christian',
      occupation: 'Teacher',
      education: 'University',
      relationshipGoal: 'marriage',
      hasChildren: false,
      isPaid: false
    },
    {
      email: '<EMAIL>',
      phone: '************',
      name: 'Jane Muthoni',
      gender: 'FEMALE',
      genderPreference: 'MALE',
      dateOfBirth: '1992-09-15',
      townIndex: 0, // Nairobi
      bio: 'Designer who loves art and fashion. Seeking someone creative and ambitious.',
      tribe: 'Kikuyu',
      religion: 'Christian',
      occupation: 'Graphic Designer',
      education: 'College',
      relationshipGoal: 'serious',
      hasChildren: false,
      isPaid: true
    }
  ]

  const createdUsers = []
  for (const userData of sampleUsers) {
    const password = await bcrypt.hash(DEMO_USER_PASSWORD, 12)
    const user = await prisma.user.create({
      data: {
        email: userData.email,
        phone: userData.phone,
        passwordHash: password,
        isPaid: userData.isPaid,
        subscriptionExpiresAt: userData.isPaid ? new Date(Date.now() + 120 * 24 * 60 * 60 * 1000) : null,
        profile: {
          create: {
            fullName: userData.name,
            gender: userData.gender,
            genderPreference: userData.genderPreference,
            dateOfBirth: new Date(userData.dateOfBirth),
            townId: towns[userData.townIndex].id,
            bio: userData.bio,
            tribe: userData.tribe,
            religion: userData.religion,
            occupation: userData.occupation,
            education: userData.education,
            relationshipGoal: userData.relationshipGoal,
            verified: Math.random() > 0.3, // 70% verified
            safetyScore: 85 + Math.random() * 15, // 85-100
            isOnline: Math.random() > 0.5
          }
        },
        children: userData.hasChildren ? {
          create: {
            count: Math.floor(Math.random() * 3) + 1,
            genders: ['MALE', 'FEMALE'].slice(0, Math.floor(Math.random() * 2) + 1)
          }
        } : undefined,
        preferences: {
          create: {
            preferredAgeMin: 25,
            preferredAgeMax: 40,
            hasChildren: 'DOESNT_MATTER',
            maxDistance: 50,
            tribePreference: null,
            religionPreference: null
          }
        },
        discoveryPreferences: {
          create: {
            showMeTo: 'everyone',
            allowMessagesFrom: 'matches',
            profileVisibility: 'public',
            locationSharing: true,
            onlineStatus: true,
            lastSeen: true
          }
        }
      },
      include: { profile: true }
    })
    createdUsers.push(user)
  }

  console.log('👥 Created sample users')

  // Create Profile Pictures
  for (const user of createdUsers) {
    await prisma.profilePicture.create({
      data: {
        profileId: user.profile!.id,
        url: 'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?w=400&h=400&fit=crop&crop=face',
        isPrimary: true,
        isApproved: true,
        fileSize: 500000 + Math.random() * 1000000,
        dimensions: { width: 400, height: 400 }
      }
    })
  }

  console.log('📸 Created profile pictures')

  // Create Matches
  const matches = []
  for (let i = 0; i < 15; i++) {
    const user1 = createdUsers[Math.floor(Math.random() * createdUsers.length)]
    const user2 = createdUsers[Math.floor(Math.random() * createdUsers.length)]
    
    if (user1.id !== user2.id) {
      const match = await prisma.match.create({
        data: {
          user1Id: user1.id,
          user2Id: user2.id,
          status: Math.random() > 0.3 ? 'accepted' : 'pending'
        }
      })
      matches.push(match)
    }
  }

  console.log('💕 Created matches')

  // Create Messages
  for (const match of matches) {
    const messageCount = Math.floor(Math.random() * 10) + 1
    for (let i = 0; i < messageCount; i++) {
      await prisma.message.create({
        data: {
          matchId: match.id,
          senderId: i % 2 === 0 ? match.user1Id : match.user2Id,
          receiverId: i % 2 === 0 ? match.user2Id : match.user1Id,
          content: [
            'Hello! How are you doing?',
            'I really enjoyed your profile!',
            'Would you like to meet for coffee sometime?',
            'What do you like to do for fun?',
            'I love traveling too! Where have you been recently?',
            'That sounds amazing! Tell me more about it.',
            'I think we have a lot in common.',
            'Looking forward to getting to know you better!',
            'What are your plans for the weekend?',
            'I love your sense of humor!'
          ][Math.floor(Math.random() * 10)],
          isRead: Math.random() > 0.3
        }
      })
    }
  }

  console.log('💬 Created messages')

  // Create Payments
  for (const user of createdUsers.filter(u => u.isPaid)) {
    const plan = plans[Math.floor(Math.random() * plans.length)]
    await prisma.payment.create({
      data: {
        userId: user.id,
        planId: plan.id,
        amount: plan.price,
        method: Math.random() > 0.5 ? 'MPESA' : 'STRIPE',
        status: 'COMPLETED',
        transactionId: `TXN${Math.random().toString(36).substring(2, 15).toUpperCase()}`,
        metadata: {
          phoneNumber: user.phone,
          paymentMethod: Math.random() > 0.5 ? 'M-Pesa' : 'Card'
        }
      }
    })
  }

  console.log('💰 Created payments')

  // Create User Activities
  for (const user of createdUsers) {
    const activityCount = Math.floor(Math.random() * 20) + 5
    for (let i = 0; i < activityCount; i++) {
      const activityTypes = ['profile_view', 'like', 'message', 'match', 'login']
      const activityType = activityTypes[Math.floor(Math.random() * activityTypes.length)]
      
      await prisma.userActivity.create({
        data: {
          userId: user.id,
          activityType: activityType,
          targetUserId: activityType !== 'login' ? 
            createdUsers[Math.floor(Math.random() * createdUsers.length)].id : null,
          metadata: {
            timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
            device: Math.random() > 0.5 ? 'mobile' : 'desktop'
          }
        }
      })
    }
  }

  console.log('📊 Created user activities')

  // Create Notifications
  for (const user of createdUsers) {
    const notificationCount = Math.floor(Math.random() * 8) + 2
    for (let i = 0; i < notificationCount; i++) {
      const notificationTypes = ['match', 'message', 'like', 'profile_view', 'system']
      const notificationType = notificationTypes[Math.floor(Math.random() * notificationTypes.length)]
      
      let title, message
      switch (notificationType) {
        case 'match':
          title = 'New Match!'
          message = 'You have a new match. Check it out!'
          break
        case 'message':
          title = 'New Message'
          message = 'Someone sent you a message.'
          break
        case 'like':
          title = 'New Like'
          message = 'Someone liked your profile!'
          break
        case 'profile_view':
          title = 'Profile View'
          message = 'Someone viewed your profile.'
          break
        case 'system':
          title = 'Welcome to KenyaMatch!'
          message = 'Thank you for joining our community.'
          break
      }

      await prisma.notification.create({
        data: {
          userId: user.id,
          type: notificationType,
          title: title,
          message: message,
          isRead: Math.random() > 0.5,
          metadata: {
            timestamp: new Date(Date.now() - Math.random() * 3 * 24 * 60 * 60 * 1000)
          }
        }
      })
    }
  }

  console.log('🔔 Created notifications')

  // Create Search History
  for (const user of createdUsers) {
    const searchCount = Math.floor(Math.random() * 5) + 1
    for (let i = 0; i < searchCount; i++) {
      const searchQueries = ['Nairobi', 'Mombasa', 'Kisumu', 'Lawyer', 'Doctor', 'Teacher']
      const query = searchQueries[Math.floor(Math.random() * searchQueries.length)]
      
      await prisma.searchHistory.create({
        data: {
          userId: user.id,
          searchQuery: query,
          filters: {
            ageMin: 25,
            ageMax: 35,
            location: 'Nairobi',
            hasChildren: 'DOESNT_MATTER'
          },
          resultsCount: Math.floor(Math.random() * 50) + 10
        }
      })
    }
  }

  console.log('🔍 Created search history')

  // Create some user blocks and reports
  for (let i = 0; i < 5; i++) {
    const blocker = createdUsers[Math.floor(Math.random() * createdUsers.length)]
    const blocked = createdUsers[Math.floor(Math.random() * createdUsers.length)]
    
    if (blocker.id !== blocked.id) {
      await prisma.blockedUser.create({
        data: {
          blockerId: blocker.id,
          blockedId: blocked.id,
          reason: 'Inappropriate behavior'
        }
      })
    }
  }

  for (let i = 0; i < 3; i++) {
    const reporter = createdUsers[Math.floor(Math.random() * createdUsers.length)]
    const reported = createdUsers[Math.floor(Math.random() * createdUsers.length)]
    
    if (reporter.id !== reported.id) {
      await prisma.report.create({
        data: {
          reporterId: reporter.id,
          reportedId: reported.id,
          reason: 'This profile appears to be fake or misleading.',
          status: 'pending'
        }
      })
    }
  }

  console.log('🚫 Created blocks and reports')

  console.log('✅ Database seeding completed successfully!')
  console.log(`📊 Created ${towns.length} towns`)
  console.log(`💳 Created ${plans.length} subscription plans`)
  console.log(`👥 Created ${createdUsers.length + 1} users (including admin)`)
  console.log(`💕 Created ${matches.length} matches`)
  console.log(`💰 Created ${createdUsers.filter(u => u.isPaid).length} payments`)
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  }) 