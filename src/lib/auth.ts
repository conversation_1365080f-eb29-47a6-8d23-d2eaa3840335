import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { jwtVerify, SignJWT } from 'jose'
import { prisma } from './prisma'

export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12)
}

export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword)
}

export function generateJWT(payload: any): string {
  return jwt.sign(payload, process.env.JWT_SECRET!, { expiresIn: '7d' })
}

export function verifyJWT(token: string): any {
  try {
    return jwt.verify(token, process.env.JWT_SECRET!)
  } catch (error) {
    return null
  }
}

// Edge Runtime compatible JWT verification for middleware
export async function verifyJWTEdge(token: string): Promise<any> {
  try {
    const secret = new TextEncoder().encode(process.env.JWT_SECRET!)
    const { payload } = await jwtVerify(token, secret)
    return payload
  } catch (error) {
    return null
  }
}

export async function getUserById(id: string) {
  return prisma.user.findUnique({
    where: { id },
    include: {
      profile: {
        include: {
          town: true,
          profilePictures: true
        }
      },
      children: true,
      preferences: true
    }
  })
}

export async function getUserByEmail(email: string) {
  return prisma.user.findUnique({
    where: { email },
    include: {
      profile: {
        include: {
          town: true,
          profilePictures: true
        }
      },
      children: true,
      preferences: true
    }
  })
}

export function isSubscriptionActive(user: any): boolean {
  if (!user.isPaid) return false
  if (!user.subscriptionExpiresAt) return false
  return new Date() < user.subscriptionExpiresAt
}

export function isProfileComplete(profile: any): boolean {
  if (!profile) return false
  
  const requiredFields = [
    'fullName',
    'gender', 
    'dateOfBirth',
    'townId'
  ]
  
  return requiredFields.every(field => profile[field] && profile[field].toString().trim() !== '')
}

export function getProfileCompletionPercentage(profile: any): number {
  if (!profile) return 0
  
  const fields = [
    'fullName',
    'gender',
    'dateOfBirth', 
    'townId',
    'bio',
    'tribe',
    'religion',
    'occupation',
    'education',
    'relationshipGoal'
  ]
  
  const completedFields = fields.filter(field => 
    profile[field] && profile[field].toString().trim() !== ''
  ).length
  
  return Math.round((completedFields / fields.length) * 100)
}

export async function enforceSubscriptionAccess(userId: string, feature: string): Promise<boolean> {
  const user = await prisma.user.findUnique({
    where: { id: userId }
  })
  
  if (!user) return false
  
  // Check if subscription is active
  if (!isSubscriptionActive(user)) {
    console.log(`User ${userId} attempted to access ${feature} without active subscription`)
    return false
  }
  
  return true
}

export async function logUserActivity(userId: string, activityType: string, targetUserId?: string, metadata?: any) {
  try {
    await prisma.userActivity.create({
      data: {
        userId,
        activityType,
        targetUserId,
        metadata: metadata || {}
      }
    })
  } catch (error) {
    console.error('Failed to log user activity:', error)
  }
} 