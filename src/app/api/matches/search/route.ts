import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyJWT, logUserActivity } from '@/lib/auth'
import { calculateAge, calculateDistance } from '@/lib/utils'

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)
    const decoded = verifyJWT(token)
    if (!decoded) {
      return NextResponse.json(
        { success: false, error: 'Invalid token' },
        { status: 401 }
      )
    }

    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      include: {
        profile: {
          include: { town: true }
        },
        preferences: true,
        children: true
      }
    })

    if (!user || !user.profile) {
      return NextResponse.json(
        { success: false, error: 'Profile not found' },
        { status: 404 }
      )
    }

    // Check if user has active subscription
    if (!user.isPaid || !user.subscriptionExpiresAt || new Date() > user.subscriptionExpiresAt) {
      return NextResponse.json(
        { success: false, error: 'Active subscription required to search for matches' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const {
      gender,
      minAge,
      maxAge,
      town,
      tribe,
      religion,
      hasChildren,
      verified,
      maxDistance,
      page = 1,
      limit = 20
    } = body

    const skip = (page - 1) * limit

    // Build advanced search filters
    const filters: any = {
      AND: [
        { id: { not: user.id } },
        { isActive: true },
        { profile: { isNot: null } }
      ]
    }

    // Gender filter
    if (gender) {
      filters.AND.push({
        profile: { gender }
      })
    }

    // Age filter
    if (minAge && maxAge) {
      const currentYear = new Date().getFullYear()
      filters.AND.push({
        profile: {
          dateOfBirth: {
            gte: new Date(currentYear - maxAge, 0, 1),
            lte: new Date(currentYear - minAge, 11, 31)
          }
        }
      })
    }

    // Location filter
    if (town) {
      filters.AND.push({
        profile: {
          town: {
            name: {
              contains: town,
              mode: 'insensitive'
            }
          }
        }
      })
    }

    // Tribe filter
    if (tribe) {
      filters.AND.push({
        profile: {
          tribe: {
            contains: tribe,
            mode: 'insensitive'
          }
        }
      })
    }

    // Religion filter
    if (religion) {
      filters.AND.push({
        profile: {
          religion: {
            contains: religion,
            mode: 'insensitive'
          }
        }
      })
    }

    // Children filter
    if (hasChildren !== undefined) {
      if (hasChildren === 'YES') {
        filters.AND.push({ children: { isNot: null } })
      } else if (hasChildren === 'NO') {
        filters.AND.push({ children: null })
      }
    }

    // Verified users only
    if (verified) {
      filters.AND.push({
        profile: { verified: true }
      })
    }

    // Exclude already matched users
    const existingMatches = await prisma.match.findMany({
      where: {
        OR: [
          { user1Id: user.id },
          { user2Id: user.id }
        ]
      },
      select: {
        user1Id: true,
        user2Id: true
      }
    })

    const matchedUserIds = existingMatches.map(match => 
      match.user1Id === user.id ? match.user2Id : match.user1Id
    )

    if (matchedUserIds.length > 0) {
      filters.AND.push({
        id: { notIn: matchedUserIds }
      })
    }

    // Exclude blocked users
    const blockedUsers = await prisma.blockedUser.findMany({
      where: {
        OR: [
          { blockerId: user.id },
          { blockedId: user.id }
        ]
      },
      select: {
        blockerId: true,
        blockedId: true
      }
    })

    const blockedUserIds = blockedUsers.map(block => 
      block.blockerId === user.id ? block.blockedId : block.blockerId
    )

    if (blockedUserIds.length > 0) {
      filters.AND.push({
        id: { notIn: blockedUserIds }
      })
    }

    // Get search results
    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where: filters,
        include: {
          profile: {
            include: {
              town: true,
              profilePictures: {
                where: { isPrimary: true },
                take: 1
              }
            }
          },
          children: true
        },
        skip,
        take: limit,
        orderBy: [
          { profile: { verified: 'desc' } },
          { profile: { lastActive: 'desc' } },
          { createdAt: 'desc' }
        ]
      }),
      prisma.user.count({ where: filters })
    ])

    // Filter by distance if specified
    let filteredUsers = users
    if (maxDistance && user.profile.town) {
      filteredUsers = users.filter(potentialMatch => {
        if (!potentialMatch.profile || !potentialMatch.profile.town) return false

        const distance = calculateDistance(
          user.profile.town.latitude,
          user.profile.town.longitude,
          potentialMatch.profile.town.latitude,
          potentialMatch.profile.town.longitude
        )

        return distance <= maxDistance
      })
    }

    // Log search activity
    await logUserActivity(user.id, 'search', undefined, {
      filters: body,
      resultsCount: filteredUsers.length
    })

    // Save search history
    await prisma.searchHistory.create({
      data: {
        userId: user.id,
        searchQuery: JSON.stringify({ gender, town, tribe, religion }),
        filters: body,
        resultsCount: filteredUsers.length
      }
    })

    return NextResponse.json({
      success: true,
      data: filteredUsers,
      pagination: {
        page,
        limit,
        total: filteredUsers.length,
        totalPages: Math.ceil(filteredUsers.length / limit)
      },
      message: `Found ${filteredUsers.length} matches`
    })

  } catch (error) {
    console.error('Advanced search error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
