import { NextRequest, NextResponse } from 'next/server'
import { verifyJWT } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyJWT(request)
    if (!authResult.success || !authResult.payload) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: authResult.payload.userId }
    })

    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Fetch payment statistics
    const [
      totalRevenue,
      totalTransactions,
      completedTransactions,
      pendingAmount
    ] = await Promise.all([
      // Total revenue from completed payments
      prisma.payment.aggregate({
        _sum: {
          amount: true
        },
        where: {
          status: 'COMPLETED'
        }
      }),
      
      // Total number of transactions
      prisma.payment.count(),
      
      // Number of completed transactions
      prisma.payment.count({
        where: {
          status: 'COMPLETED'
        }
      }),
      
      // Pending amount
      prisma.payment.aggregate({
        _sum: {
          amount: true
        },
        where: {
          status: 'PENDING'
        }
      })
    ])

    const successRate = totalTransactions > 0 
      ? Math.round((completedTransactions / totalTransactions) * 100) 
      : 0

    const stats = {
      totalRevenue: totalRevenue._sum.amount || 0,
      totalTransactions,
      successRate,
      pendingAmount: pendingAmount._sum.amount || 0
    }

    return NextResponse.json(stats)

  } catch (error) {
    console.error('Payment stats error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch payment statistics' },
      { status: 500 }
    )
  }
}
