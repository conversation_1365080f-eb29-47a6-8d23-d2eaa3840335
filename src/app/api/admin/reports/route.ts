import { NextRequest, NextResponse } from 'next/server'
import { verifyJWT } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyJWT(request)
    if (!authResult.success || !authResult.payload) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: authResult.payload.userId }
    })

    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || 'all'
    const type = searchParams.get('type') || 'all'

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}

    // Search filter
    if (search) {
      where.OR = [
        { reporter: { email: { contains: search, mode: 'insensitive' } } },
        { reportedUser: { email: { contains: search, mode: 'insensitive' } } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }

    // Status filter
    if (status !== 'all') {
      where.status = status
    }

    // Type filter
    if (type !== 'all') {
      where.type = type
    }

    // Fetch reports and total count
    const [reports, totalCount] = await Promise.all([
      prisma.report.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          reporter: {
            select: {
              email: true,
              name: true
            }
          },
          reportedUser: {
            select: {
              email: true,
              name: true
            }
          }
        }
      }),
      prisma.report.count({ where })
    ])

    // Format reports data
    const formattedReports = reports.map(report => ({
      id: report.id,
      reporterId: report.reporterId,
      reporterEmail: report.reporter.email,
      reportedUserId: report.reportedUserId,
      reportedUserEmail: report.reportedUser.email,
      reportedUserName: report.reportedUser.name,
      type: report.type,
      description: report.description,
      status: report.status,
      createdAt: report.createdAt.toISOString(),
      reviewedAt: report.reviewedAt?.toISOString(),
      reviewedBy: report.reviewedBy,
      adminNotes: report.adminNotes
    }))

    const totalPages = Math.ceil(totalCount / limit)

    return NextResponse.json({
      reports: formattedReports,
      totalCount,
      totalPages,
      currentPage: page
    })

  } catch (error) {
    console.error('Admin reports error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch reports' },
      { status: 500 }
    )
  }
}
