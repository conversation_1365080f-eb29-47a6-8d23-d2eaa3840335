import { NextRequest, NextResponse } from 'next/server'
import { verifyJWT } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { sendEmail } from '@/lib/email'

interface RouteParams {
  params: {
    id: string
    action: string
  }
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    // Verify admin authentication
    const authResult = await verifyJWT(request)
    if (!authResult.success || !authResult.payload) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const adminUser = await prisma.user.findUnique({
      where: { id: authResult.payload.userId }
    })

    if (!adminUser || adminUser.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const { id, action } = params

    // Validate user exists
    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        email: true,
        name: true,
        isActive: true
      }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Handle different actions
    switch (action) {
      case 'activate':
        if (user.isActive) {
          return NextResponse.json({ error: 'User is already active' }, { status: 400 })
        }
        
        await prisma.user.update({
          where: { id },
          data: { isActive: true }
        })

        // Send reactivation email
        try {
          await sendEmail({
            to: user.email,
            subject: 'Your KenyaMatch Account Has Been Reactivated',
            html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #ec4899;">Account Reactivated</h2>
                <p>Hello ${user.name || 'there'},</p>
                <p>Your KenyaMatch account has been reactivated by our admin team.</p>
                <p>You can now log in and continue using our platform to find meaningful connections.</p>
                <p>If you have any questions, please contact our support team.</p>
                <p>Best regards,<br>The KenyaMatch Team</p>
              </div>
            `
          })
        } catch (emailError) {
          console.error('Failed to send reactivation email:', emailError)
        }
        
        break

      case 'deactivate':
        if (!user.isActive) {
          return NextResponse.json({ error: 'User is already inactive' }, { status: 400 })
        }
        
        await prisma.user.update({
          where: { id },
          data: { isActive: false }
        })

        // Send deactivation email
        try {
          await sendEmail({
            to: user.email,
            subject: 'Your KenyaMatch Account Has Been Deactivated',
            html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #ec4899;">Account Deactivated</h2>
                <p>Hello ${user.name || 'there'},</p>
                <p>Your KenyaMatch account has been temporarily deactivated by our admin team.</p>
                <p>This may be due to a violation of our community guidelines or terms of service.</p>
                <p>If you believe this is an error or would like to appeal this decision, please contact our support team.</p>
                <p>Best regards,<br>The KenyaMatch Team</p>
              </div>
            `
          })
        } catch (emailError) {
          console.error('Failed to send deactivation email:', emailError)
        }
        
        break

      case 'send-email':
        // Send a general admin message
        try {
          await sendEmail({
            to: user.email,
            subject: 'Message from KenyaMatch Admin Team',
            html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #ec4899;">Message from Admin Team</h2>
                <p>Hello ${user.name || 'there'},</p>
                <p>This is a message from the KenyaMatch admin team.</p>
                <p>We're reaching out to ensure you're having a great experience on our platform.</p>
                <p>If you have any questions or need assistance, please don't hesitate to contact us.</p>
                <p>Thank you for being part of the KenyaMatch community!</p>
                <p>Best regards,<br>The KenyaMatch Team</p>
              </div>
            `
          })
        } catch (emailError) {
          console.error('Failed to send admin email:', emailError)
          return NextResponse.json({ error: 'Failed to send email' }, { status: 500 })
        }
        
        break

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }

    return NextResponse.json({
      success: true,
      message: `User ${action} completed successfully`
    })

  } catch (error) {
    console.error('User action error:', error)
    return NextResponse.json(
      { error: 'Failed to perform user action' },
      { status: 500 }
    )
  }
}
