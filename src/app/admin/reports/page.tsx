'use client'

import { useEffect, useState } from 'react'
import { Search, Filter, AlertTriangle, Eye, CheckCircle, XCircle, Clock, Flag, User, MessageCircle } from 'lucide-react'

interface Report {
  id: string
  reporterId: string
  reporterEmail: string
  reportedUserId: string
  reportedUserEmail: string
  reportedUserName?: string
  type: 'INAPPROPRIATE_CONTENT' | 'HARASSMENT' | 'FAKE_PROFILE' | 'SPAM' | 'OTHER'
  description: string
  status: 'PENDING' | 'REVIEWED' | 'RESOLVED' | 'DISMISSED'
  createdAt: string
  reviewedAt?: string
  reviewedBy?: string
  adminNotes?: string
}

export default function AdminReports() {
  const [reports, setReports] = useState<Report[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [filterType, setFilterType] = useState('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [selectedReport, setSelectedReport] = useState<Report | null>(null)
  const [showModal, setShowModal] = useState(false)

  useEffect(() => {
    fetchReports()
  }, [currentPage, searchTerm, filterStatus, filterType])

  const fetchReports = async () => {
    try {
      const token = localStorage.getItem('kenyamatch_token')
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        search: searchTerm,
        status: filterStatus,
        type: filterType
      })

      const response = await fetch(`/api/admin/reports?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        setReports(data.reports || [])
        setTotalPages(data.totalPages || 1)
      }
    } catch (error) {
      console.error('Failed to fetch reports:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleReportAction = async (reportId: string, action: string, notes?: string) => {
    try {
      const token = localStorage.getItem('kenyamatch_token')
      const response = await fetch(`/api/admin/reports/${reportId}/${action}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ adminNotes: notes })
      })
      
      if (response.ok) {
        fetchReports() // Refresh the list
        setShowModal(false)
        setSelectedReport(null)
      }
    } catch (error) {
      console.error(`Failed to ${action} report:`, error)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'REVIEWED':
        return <Eye className="h-4 w-4 text-blue-500" />
      case 'RESOLVED':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'DISMISSED':
        return <XCircle className="h-4 w-4 text-gray-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    const baseClasses = "px-2 py-1 text-xs font-medium rounded-full"
    switch (status) {
      case 'PENDING':
        return <span className={`${baseClasses} bg-yellow-100 text-yellow-700`}>Pending</span>
      case 'REVIEWED':
        return <span className={`${baseClasses} bg-blue-100 text-blue-700`}>Reviewed</span>
      case 'RESOLVED':
        return <span className={`${baseClasses} bg-green-100 text-green-700`}>Resolved</span>
      case 'DISMISSED':
        return <span className={`${baseClasses} bg-gray-100 text-gray-700`}>Dismissed</span>
      default:
        return <span className={`${baseClasses} bg-gray-100 text-gray-700`}>Unknown</span>
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'INAPPROPRIATE_CONTENT':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'HARASSMENT':
        return <MessageCircle className="h-4 w-4 text-red-600" />
      case 'FAKE_PROFILE':
        return <User className="h-4 w-4 text-orange-500" />
      case 'SPAM':
        return <Flag className="h-4 w-4 text-purple-500" />
      default:
        return <Flag className="h-4 w-4 text-gray-400" />
    }
  }

  const formatReportType = (type: string) => {
    return type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
  }

  const getPriorityColor = (type: string) => {
    switch (type) {
      case 'HARASSMENT':
        return 'border-l-red-500 bg-red-50'
      case 'INAPPROPRIATE_CONTENT':
        return 'border-l-orange-500 bg-orange-50'
      case 'FAKE_PROFILE':
        return 'border-l-yellow-500 bg-yellow-50'
      default:
        return 'border-l-gray-300 bg-white'
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Reports</h1>
        </div>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow-sm p-6 animate-pulse">
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">User Reports</h1>
          <p className="text-gray-600 mt-1">Review and manage user safety reports</p>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-500">Pending Reports</p>
          <p className="text-2xl font-bold text-red-600">
            {reports.filter(r => r.status === 'PENDING').length}
          </p>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search by reporter or reported user email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
            />
          </div>
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-400" />
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="PENDING">Pending</option>
              <option value="REVIEWED">Reviewed</option>
              <option value="RESOLVED">Resolved</option>
              <option value="DISMISSED">Dismissed</option>
            </select>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
            >
              <option value="all">All Types</option>
              <option value="HARASSMENT">Harassment</option>
              <option value="INAPPROPRIATE_CONTENT">Inappropriate Content</option>
              <option value="FAKE_PROFILE">Fake Profile</option>
              <option value="SPAM">Spam</option>
              <option value="OTHER">Other</option>
            </select>
          </div>
        </div>
      </div>

      {/* Reports List */}
      <div className="space-y-4">
        {reports.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-12 text-center">
            <Flag className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p className="text-lg font-medium text-gray-500">No reports found</p>
            <p className="text-sm text-gray-400">Try adjusting your search or filter criteria</p>
          </div>
        ) : (
          reports.map((report) => (
            <div
              key={report.id}
              className={`border-l-4 rounded-lg shadow-sm p-6 ${getPriorityColor(report.type)}`}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-3">
                    {getTypeIcon(report.type)}
                    <h3 className="text-lg font-semibold text-gray-900">
                      {formatReportType(report.type)}
                    </h3>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(report.status)}
                      {getStatusBadge(report.status)}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <p className="text-sm text-gray-600">Reported User</p>
                      <p className="font-medium text-gray-900">
                        {report.reportedUserName || 'No name'}
                      </p>
                      <p className="text-sm text-gray-500">{report.reportedUserEmail}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Reported By</p>
                      <p className="text-sm text-gray-500">{report.reporterEmail}</p>
                      <p className="text-xs text-gray-400">
                        {new Date(report.createdAt).toLocaleDateString()} at{' '}
                        {new Date(report.createdAt).toLocaleTimeString()}
                      </p>
                    </div>
                  </div>

                  <div className="mb-4">
                    <p className="text-sm text-gray-600 mb-1">Description</p>
                    <p className="text-gray-900 bg-white p-3 rounded border">
                      {report.description}
                    </p>
                  </div>

                  {report.adminNotes && (
                    <div className="mb-4">
                      <p className="text-sm text-gray-600 mb-1">Admin Notes</p>
                      <p className="text-gray-700 bg-blue-50 p-3 rounded border border-blue-200">
                        {report.adminNotes}
                      </p>
                    </div>
                  )}
                </div>

                <div className="ml-6 flex flex-col gap-2">
                  {report.status === 'PENDING' && (
                    <>
                      <button
                        onClick={() => {
                          setSelectedReport(report)
                          setShowModal(true)
                        }}
                        className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                      >
                        Review
                      </button>
                      <button
                        onClick={() => handleReportAction(report.id, 'dismiss')}
                        className="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
                      >
                        Dismiss
                      </button>
                    </>
                  )}
                  {report.status === 'REVIEWED' && (
                    <button
                      onClick={() => handleReportAction(report.id, 'resolve')}
                      className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
                    >
                      Resolve
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Review Modal */}
      {showModal && selectedReport && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Review Report</h3>
            <p className="text-gray-600 mb-4">
              Add notes about your review of this report:
            </p>
            <textarea
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
              rows={4}
              placeholder="Enter your review notes..."
              id="adminNotes"
            />
            <div className="flex gap-3 mt-4">
              <button
                onClick={() => {
                  const notes = (document.getElementById('adminNotes') as HTMLTextAreaElement)?.value
                  handleReportAction(selectedReport.id, 'review', notes)
                }}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                Mark as Reviewed
              </button>
              <button
                onClick={() => {
                  setShowModal(false)
                  setSelectedReport(null)
                }}
                className="flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
